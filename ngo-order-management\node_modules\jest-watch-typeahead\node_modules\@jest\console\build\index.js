'use strict';

Object.defineProperty(exports, '__esModule', {
  value: true
});
Object.defineProperty(exports, 'BufferedConsole', {
  enumerable: true,
  get: function () {
    return _BufferedConsole.default;
  }
});
Object.defineProperty(exports, 'CustomConsole', {
  enumerable: true,
  get: function () {
    return _CustomConsole.default;
  }
});
Object.defineProperty(exports, 'NullConsole', {
  enumerable: true,
  get: function () {
    return _NullConsole.default;
  }
});
Object.defineProperty(exports, 'getConsoleOutput', {
  enumerable: true,
  get: function () {
    return _getConsoleOutput.default;
  }
});

var _BufferedConsole = _interopRequireDefault(require('./BufferedConsole'));

var _CustomConsole = _interopRequireDefault(require('./CustomConsole'));

var _NullConsole = _interopRequireDefault(require('./NullConsole'));

var _getConsoleOutput = _interopRequireDefault(require('./getConsoleOutput'));

function _interopRequireDefault(obj) {
  return obj && obj.__esModule ? obj : {default: obj};
}
