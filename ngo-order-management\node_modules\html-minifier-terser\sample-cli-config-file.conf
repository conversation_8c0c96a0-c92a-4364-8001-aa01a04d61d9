{"caseSensitive": false, "collapseBooleanAttributes": true, "collapseInlineTagWhitespace": false, "collapseWhitespace": true, "conservativeCollapse": false, "continueOnParseError": true, "customAttrCollapse": ".*", "decodeEntities": true, "html5": true, "ignoreCustomFragments": ["<#[\\s\\S]*?#>", "<%[\\s\\S]*?%>", "<\\?[\\s\\S]*?\\?>"], "includeAutoGeneratedTags": false, "keepClosingSlash": false, "maxLineLength": 0, "minifyCSS": true, "minifyJS": true, "preserveLineBreaks": false, "preventAttributesEscaping": false, "processConditionalComments": true, "processScripts": ["text/html"], "removeAttributeQuotes": true, "removeComments": true, "removeEmptyAttributes": true, "removeEmptyElements": true, "removeOptionalTags": true, "removeRedundantAttributes": true, "removeScriptTypeAttributes": true, "removeStyleLinkTypeAttributes": true, "removeTagWhitespace": true, "sortAttributes": true, "sortClassName": true, "trimCustomFragments": true, "useShortDoctype": true}